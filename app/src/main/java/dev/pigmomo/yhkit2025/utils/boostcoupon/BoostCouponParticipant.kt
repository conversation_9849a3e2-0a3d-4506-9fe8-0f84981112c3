package dev.pigmomo.yhkit2025.utils.boostcoupon

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.utils.ResponseParserUtils
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.utils.FailedTokenIndexRecordUtils
import dev.pigmomo.yhkit2025.utils.GameCodeManager
import dev.pigmomo.yhkit2025.utils.ProcessRecorder
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withTimeout

/**
 * 助力券参与器
 * 
 * 专门处理助力券的参与逻辑，包括批量助力处理和状态更新
 */
object BoostCouponParticipant {
    private const val TAG = "BoostCouponParticipant"
    
    /**
     * 参与助力券
     * 
     * @param context 上下文
     * @param requestService 请求服务
     * @param progressRecorder 进度记录器
     * @param token 用户令牌
     * @param tokenIndex 令牌索引
     * @param config 配置信息
     * @return 操作结果
     */
    suspend fun participate(
        context: Context,
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        token: OrderTokenEntity,
        tokenIndex: Int,
        config: BoostCouponManager.BoostCouponConfig
    ): BoostCouponManager.OperationResult {
        
        return try {
            withTimeout(config.timeoutMs) {
                executeParticipation(
                    context = context,
                    requestService = requestService,
                    progressRecorder = progressRecorder,
                    token = token,
                    tokenIndex = tokenIndex,
                    config = config
                )
            }
        } catch (e: TimeoutCancellationException) {
            val errorMsg = "参与助力券操作超时 (${config.timeoutMs}ms)"
            progressRecorder.recordProcess(errorMsg, "ERROR")
            Log.e(TAG, errorMsg, e)
            BoostCouponManager.OperationResult.failure(message = errorMsg)
        } catch (e: Exception) {
            val errorMsg = "参与助力券操作发生异常: ${e.message}"
            progressRecorder.recordProcess(errorMsg, "ERROR")
            Log.e(TAG, errorMsg, e)
            BoostCouponManager.OperationResult.failure(message = errorMsg)
        }
    }
    
    /**
     * 执行参与助力券的核心逻辑
     */
    private suspend fun executeParticipation(
        context: Context,
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        token: OrderTokenEntity,
        tokenIndex: Int,
        config: BoostCouponManager.BoostCouponConfig
    ): BoostCouponManager.OperationResult {
        
        // 使用锁定管理器安全获取游戏码
        val gameCodeInfoList = BoostCouponLockManager.safeExecuteWithLock(
            context = context,
            excludePhoneNumber = token.phoneNumber,
            maxCount = config.maxParticipateCount,
            timeoutMs = config.timeoutMs
        ) { gameCodes ->
            if (gameCodes.isEmpty()) {
                handleEmptyGameCodes(context, progressRecorder, token, tokenIndex)
                return@safeExecuteWithLock BoostCouponManager.OperationResult.failure(message = "没有可用的游戏码")
            }
            
            processGameCodes(
                context = context,
                requestService = requestService,
                progressRecorder = progressRecorder,
                gameCodeInfoList = gameCodes,
                tokenIndex = tokenIndex,
                config = config
            )
        }
        
        return gameCodeInfoList
    }
    
    /**
     * 处理游戏码为空的情况
     */
    private fun handleEmptyGameCodes(
        context: Context,
        progressRecorder: ProcessRecorder,
        token: OrderTokenEntity,
        tokenIndex: Int
    ) {
        // 执行详细诊断以提供更准确的错误信息
        val diagnosticResult = BoostCouponDiagnosticService.performFullDiagnosis(context, token.phoneNumber)
        
        progressRecorder.recordProcess("gameCodeList为空，停止参加助力券操作", "WARNING")
        progressRecorder.recordProcess("诊断结果: ${diagnosticResult.summary}", "INFO")
        diagnosticResult.recommendations.forEach { recommendation ->
            progressRecorder.recordProcess("建议: $recommendation", "INFO")
        }
        
        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
            FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
            tokenIndex,
            "助力券"
        )
    }
    
    /**
     * 处理游戏码列表
     */
    private suspend fun processGameCodes(
        context: Context,
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        gameCodeInfoList: List<GameCodeManager.GameCodeInfo>,
        tokenIndex: Int,
        config: BoostCouponManager.BoostCouponConfig
    ): BoostCouponManager.OperationResult {
        
        progressRecorder.recordProcess("共${gameCodeInfoList.size}个助力券参数，开始参与助力券操作")
        
        var successCount = 0
        var failCount = 0
        val details = mutableListOf<String>()
        
        // 处理每一个游戏码
        for ((index, gameCodeInfo) in gameCodeInfoList.withIndex()) {
            try {
                val result = processSingleGameCode(
                    context = context,
                    requestService = requestService,
                    progressRecorder = progressRecorder,
                    gameCodeInfo = gameCodeInfo,
                    index = index,
                    total = gameCodeInfoList.size,
                    tokenIndex = tokenIndex,
                    config = config
                )
                
                if (result.success) {
                    successCount++
                    details.add("成功: ${gameCodeInfo.prizeId}:${gameCodeInfo.gameCode}")
                } else {
                    failCount++
                    details.add("失败: ${gameCodeInfo.prizeId}:${gameCodeInfo.gameCode} - ${result.message}")
                }
                
            } catch (e: Exception) {
                failCount++
                val errorMsg = "处理游戏码异常: ${gameCodeInfo.prizeId}:${gameCodeInfo.gameCode} - ${e.message}"
                progressRecorder.recordProcess(errorMsg, "ERROR")
                details.add("异常: ${gameCodeInfo.prizeId}:${gameCodeInfo.gameCode} - ${e.message}")
                Log.e(TAG, errorMsg, e)
            }
        }
        
        val finalMessage = "助力券参与完成: 成功${successCount}个, 失败${failCount}个"
        progressRecorder.recordProcess(finalMessage)
        
        return BoostCouponManager.OperationResult.mixed(
            successCount = successCount,
            failCount = failCount,
            message = finalMessage
        ).copy(details = details)
    }
    
    /**
     * 处理单个游戏码
     */
    private suspend fun processSingleGameCode(
        context: Context,
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        gameCodeInfo: GameCodeManager.GameCodeInfo,
        index: Int,
        total: Int,
        tokenIndex: Int,
        config: BoostCouponManager.BoostCouponConfig
    ): BoostCouponManager.OperationResult {
        
        val prizeId = gameCodeInfo.prizeId
        val gameCode = gameCodeInfo.gameCode
        val phoneNumber = gameCodeInfo.phoneNumber
        
        progressRecorder.recordProcess("参与助力券 ${index + 1}/$total: prizeId=${prizeId}&gameCode=${gameCode}, 手机号=${phoneNumber}")
        
        // 调用API参与助力，使用带超时控制的协程
        val boostResult = withTimeout(config.timeoutMs) {
            requestService.coupon.boostCoupon(gameCode)
        }
        
        return when (boostResult) {
            is RequestResult.Success -> {
                handleBoostSuccess(
                    context = context,
                    progressRecorder = progressRecorder,
                    gameCodeInfo = gameCodeInfo,
                    boostResult = boostResult,
                    tokenIndex = tokenIndex
                )
            }
            
            is RequestResult.Error -> {
                handleBoostError(
                    progressRecorder = progressRecorder,
                    gameCodeInfo = gameCodeInfo,
                    error = boostResult.error,
                    tokenIndex = tokenIndex
                )
            }
        }
    }
    
    /**
     * 处理助力成功的响应
     */
    private fun handleBoostSuccess(
        context: Context,
        progressRecorder: ProcessRecorder,
        gameCodeInfo: GameCodeManager.GameCodeInfo,
        boostResult: RequestResult.Success<String>,
        tokenIndex: Int
    ): BoostCouponManager.OperationResult {
        
        val detailedResponse = ResponseParserUtils.parseBoostCouponResponse(boostResult.data)
        
        if (detailedResponse != null) {
            if (detailedResponse.code == 0) {
                return handleSuccessfulBoost(context, progressRecorder, gameCodeInfo, detailedResponse)
            } else {
                return handleBoostFailure(context, progressRecorder, gameCodeInfo, detailedResponse, tokenIndex)
            }
        } else {
            // 释放gameCode标记
            GameCodeManager.releaseGameCode(gameCodeInfo.prizeId, gameCodeInfo.gameCode)
            progressRecorder.recordProcess(
                "解析助力响应错误: prizeId=${gameCodeInfo.prizeId}&gameCode=${gameCodeInfo.gameCode}",
                "ERROR"
            )
            return BoostCouponManager.OperationResult.failure(message = "解析响应失败")
        }
    }
    
    /**
     * 处理助力失败的响应
     */
    private fun handleBoostError(
        progressRecorder: ProcessRecorder,
        gameCodeInfo: GameCodeManager.GameCodeInfo,
        error: Throwable,
        tokenIndex: Int
    ): BoostCouponManager.OperationResult {
        
        // 释放gameCode标记
        GameCodeManager.releaseGameCode(gameCodeInfo.prizeId, gameCodeInfo.gameCode)
        
        val errorMsg = "参与助力请求错误: prizeId=${gameCodeInfo.prizeId}&gameCode=${gameCodeInfo.gameCode}, ${error.message}"
        progressRecorder.recordProcess(errorMsg, "ERROR")
        
        FailedTokenIndexRecordUtils.recordFailedTokenIndex(
            FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
            tokenIndex,
            "助力券"
        )
        
        Log.e(TAG, errorMsg, error)
        return BoostCouponManager.OperationResult.failure(message = error.message ?: "请求错误")
    }
    
    /**
     * 处理成功的助力响应
     */
    private fun handleSuccessfulBoost(
        context: Context,
        progressRecorder: ProcessRecorder,
        gameCodeInfo: GameCodeManager.GameCodeInfo,
        response: dev.pigmomo.yhkit2025.api.model.coupon.BoostCouponResponse
    ): BoostCouponManager.OperationResult {
        
        // 获取助力状态信息
        val boostInfo = response.data?.boostCouponBoostDTO
        val status = boostInfo?.status ?: 0
        val statusDesc = when (status) {
            1 -> "助力成功"
            2 -> "助力完成"
            else -> "状态未知($status)"
        }
        
        progressRecorder.recordProcess(
            "助力成功: prizeId=${gameCodeInfo.prizeId}&gameCode=${gameCodeInfo.gameCode}, " +
            "手机号=${gameCodeInfo.phoneNumber}, 状态=${statusDesc}"
        )
        
        // 如果状态为"助力完成"，则从文件中删除该gameCode
        if (status == 2) {
            val deleted = GameCodeManager.deleteGameCode(context, gameCodeInfo.prizeId, gameCodeInfo.gameCode)
            if (deleted) {
                progressRecorder.recordProcess("助力完成，已从文件中删除: prizeId=${gameCodeInfo.prizeId}&gameCode=${gameCodeInfo.gameCode}")
            } else {
                progressRecorder.recordProcess(
                    "助力完成，但删除错误: prizeId=${gameCodeInfo.prizeId}&gameCode=${gameCodeInfo.gameCode}",
                    "ERROR"
                )
            }
        } else {
            // 助力成功但未完成，释放gameCode标记
            GameCodeManager.releaseGameCode(gameCodeInfo.prizeId, gameCodeInfo.gameCode)
        }
        
        return BoostCouponManager.OperationResult.success(message = statusDesc)
    }
    
    /**
     * 处理助力失败的响应
     */
    private fun handleBoostFailure(
        context: Context,
        progressRecorder: ProcessRecorder,
        gameCodeInfo: GameCodeManager.GameCodeInfo,
        response: dev.pigmomo.yhkit2025.api.model.coupon.BoostCouponResponse,
        tokenIndex: Int
    ): BoostCouponManager.OperationResult {
        
        val message = response.message
        
        // 特殊处理某些错误消息
        when (message) {
            "本期好友助力已结束" -> {
                val deleted = GameCodeManager.deleteGameCode(context, gameCodeInfo.prizeId, gameCodeInfo.gameCode)
                if (deleted) {
                    progressRecorder.recordProcess("助力已结束，已从文件中删除: prizeId=${gameCodeInfo.prizeId}&gameCode=${gameCodeInfo.gameCode}")
                } else {
                    progressRecorder.recordProcess(
                        "助力已结束，但删除错误: prizeId=${gameCodeInfo.prizeId}&gameCode=${gameCodeInfo.gameCode}",
                        "ERROR"
                    )
                }
                progressRecorder.recordProcess("本期好友助力已结束，停止参与助力券", "WARNING")
            }
            
            "每日助力次数超过限制" -> {
                // 不记录为失败令牌
                GameCodeManager.releaseGameCode(gameCodeInfo.prizeId, gameCodeInfo.gameCode)
            }
            
            else -> {
                FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                    FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                    tokenIndex,
                    "助力券"
                )
                GameCodeManager.releaseGameCode(gameCodeInfo.prizeId, gameCodeInfo.gameCode)
            }
        }
        
        progressRecorder.recordProcess(
            "参与助力错误: prizeId=${gameCodeInfo.prizeId}&gameCode=${gameCodeInfo.gameCode}, " +
            "手机号=${gameCodeInfo.phoneNumber}, $message",
            "WARNING"
        )
        
        return BoostCouponManager.OperationResult.failure(message = message ?: "未知错误")
    }
}
