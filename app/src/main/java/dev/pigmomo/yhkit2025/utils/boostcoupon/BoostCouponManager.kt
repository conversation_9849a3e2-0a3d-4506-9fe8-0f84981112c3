package dev.pigmomo.yhkit2025.utils.boostcoupon

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.utils.ProcessRecorder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 助力券管理器
 * 
 * 统一管理助力券的发起、参与、状态查询等核心功能
 * 替代原有分散在BatchOperationUtils中的复杂逻辑
 */
object BoostCouponManager {
    private const val TAG = "BoostCouponManager"
    
    // 子组件
    private val initiator = BoostCouponInitiator
    private val participant = BoostCouponParticipant
    private val diagnosticService = BoostCouponDiagnosticService
    
    /**
     * 操作结果封装类
     */
    data class OperationResult(
        val success: Boolean,
        val successCount: Int = 0,
        val failCount: Int = 0,
        val message: String = "",
        val details: List<String> = emptyList()
    ) {
        companion object {
            fun success(successCount: Int = 1, message: String = "操作成功") = 
                OperationResult(true, successCount, 0, message)
                
            fun failure(failCount: Int = 1, message: String = "操作失败") = 
                OperationResult(false, 0, failCount, message)
                
            fun mixed(successCount: Int, failCount: Int, message: String) = 
                OperationResult(successCount > 0, successCount, failCount, message)
        }
    }
    
    /**
     * 助力券配置
     */
    data class BoostCouponConfig(
        val timeoutMs: Long = 30_000L,
        val maxRetryCount: Int = 3,
        val delayBetweenRequests: Long = 1000L,
        val maxParticipateCount: Int = 10
    )
    
    private var config = BoostCouponConfig()
    
    /**
     * 更新配置
     */
    fun updateConfig(newConfig: BoostCouponConfig) {
        config = newConfig
        Log.d(TAG, "Config updated: $config")
    }
    
    /**
     * 发起助力券
     * 
     * @param context 上下文
     * @param requestService 请求服务
     * @param progressRecorder 进度记录器
     * @param token 用户令牌
     * @param prizeIds 奖品ID列表
     * @param tokenIndex 令牌索引（用于错误记录）
     * @return 操作结果
     */
    suspend fun initiateBoostCoupons(
        context: Context,
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        token: OrderTokenEntity,
        prizeIds: List<String>,
        tokenIndex: Int = 0
    ): OperationResult = withContext(Dispatchers.IO) {
        
        if (prizeIds.isEmpty()) {
            val message = "助力券配置数据为空，无法发起助力"
            progressRecorder.recordProcess(message, "WARNING")
            return@withContext OperationResult.failure(message = message)
        }
        
        progressRecorder.recordProcess("开始发起助力券，共${prizeIds.size}个")
        
        return@withContext initiator.initiate(
            context = context,
            requestService = requestService,
            progressRecorder = progressRecorder,
            token = token,
            prizeIds = prizeIds,
            tokenIndex = tokenIndex,
            config = config
        )
    }
    
    /**
     * 参与助力券
     * 
     * @param context 上下文
     * @param requestService 请求服务
     * @param progressRecorder 进度记录器
     * @param token 用户令牌
     * @param tokenIndex 令牌索引（用于错误记录）
     * @return 操作结果
     */
    suspend fun participateBoostCoupons(
        context: Context,
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        token: OrderTokenEntity,
        tokenIndex: Int = 0
    ): OperationResult = withContext(Dispatchers.IO) {
        
        progressRecorder.recordProcess("开始参加助力券操作，从文件读取gameCode")
        
        // 执行快速诊断
        val quickDiagnosisResult = diagnosticService.quickDiagnosis(context, token.phoneNumber)
        progressRecorder.recordProcess("助力券状态: $quickDiagnosisResult")
        
        return@withContext participant.participate(
            context = context,
            requestService = requestService,
            progressRecorder = progressRecorder,
            token = token,
            tokenIndex = tokenIndex,
            config = config
        )
    }
    
    /**
     * 获取助力券状态诊断
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @return 诊断结果字符串
     */
    fun getDiagnosticInfo(context: Context, excludePhoneNumber: String? = null): String {
        return diagnosticService.quickDiagnosis(context, excludePhoneNumber)
    }
    
    /**
     * 获取详细诊断信息
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @return 详细诊断结果
     */
    fun getDetailedDiagnostic(context: Context, excludePhoneNumber: String? = null): BoostCouponDiagnosticService.DiagnosticResult {
        return diagnosticService.performFullDiagnosis(context, excludePhoneNumber)
    }
    
    /**
     * 清理过期的锁定状态
     * 
     * @return 清理的锁定数量
     */
    fun cleanupExpiredLocks(): Int {
        return BoostCouponLockManager.cleanupExpiredLocks()
    }
    
    /**
     * 强制清理所有锁定状态（紧急恢复）
     * 
     * @return 清理的锁定数量
     */
    fun forceCleanupAllLocks(): Int {
        return BoostCouponLockManager.forceCleanupAllLocks()
    }
    
    /**
     * 获取当前锁定状态统计
     * 
     * @return Pair<总锁定数, 即将过期数>
     */
    fun getLockStatistics(): Pair<Int, Int> {
        return BoostCouponLockManager.getLockStatistics()
    }
}
