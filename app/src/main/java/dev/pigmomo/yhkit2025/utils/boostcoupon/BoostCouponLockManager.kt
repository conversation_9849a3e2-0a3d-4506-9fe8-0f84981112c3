package dev.pigmomo.yhkit2025.utils.boostcoupon

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.utils.GameCodeManager
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withTimeout
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * 助力券锁定管理器
 * 
 * 统一管理助力券游戏码的锁定机制，提供安全的并发访问控制
 */
object BoostCouponLockManager {
    private const val TAG = "BoostCouponLockManager"
    
    // 锁定超时时间（毫秒）- 15秒
    private const val LOCK_TIMEOUT_MS = 15_000L
    
    // 清理任务执行间隔（毫秒）- 5秒
    private const val CLEANUP_INTERVAL_MS = 5_000L
    
    // 记录正在使用中的gameCode及其锁定时间
    private val lockedGameCodes = ConcurrentHashMap<String, Long>()
    
    // 定时清理任务执行器
    private val cleanupExecutor: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor { r ->
        Thread(r, "BoostCouponLockManager-Cleanup").apply {
            isDaemon = true
        }
    }
    
    init {
        // 启动定时清理任务
        startCleanupTask()
    }
    
    /**
     * 锁定状态信息类
     */
    data class LockInfo(
        val key: String,
        val lockTime: Long,
        val remainingTime: Long
    )
    
    /**
     * 启动定时清理任务
     */
    private fun startCleanupTask() {
        cleanupExecutor.scheduleWithFixedDelay({
            try {
                cleanupExpiredLocks()
            } catch (e: Exception) {
                Log.e(TAG, "Error during cleanup task", e)
            }
        }, CLEANUP_INTERVAL_MS, CLEANUP_INTERVAL_MS, TimeUnit.MILLISECONDS)
        
        Log.d(TAG, "Cleanup task started with interval: ${CLEANUP_INTERVAL_MS}ms")
    }
    
    /**
     * 清理过期的锁定
     * 
     * @return 清理的锁定数量
     */
    fun cleanupExpiredLocks(): Int {
        val currentTime = System.currentTimeMillis()
        val expiredKeys = mutableListOf<String>()
        
        // 查找过期的锁定
        lockedGameCodes.forEach { (key, lockTime) ->
            if (currentTime - lockTime > LOCK_TIMEOUT_MS) {
                expiredKeys.add(key)
            }
        }
        
        // 移除过期的锁定
        if (expiredKeys.isNotEmpty()) {
            expiredKeys.forEach { key ->
                lockedGameCodes.remove(key)
            }
            Log.d(TAG, "Cleaned up ${expiredKeys.size} expired locks: $expiredKeys")
        }
        
        return expiredKeys.size
    }
    
    /**
     * 强制清理所有锁定状态（紧急恢复机制）
     * 
     * @return 清理的锁定数量
     */
    fun forceCleanupAllLocks(): Int {
        val count = lockedGameCodes.size
        lockedGameCodes.clear()
        Log.w(TAG, "Force cleaned up all $count locks")
        return count
    }
    
    /**
     * 获取当前锁定状态信息
     * 
     * @return 锁定状态信息列表
     */
    fun getLockStatusInfo(): List<LockInfo> {
        val currentTime = System.currentTimeMillis()
        return lockedGameCodes.map { (key, lockTime) ->
            val remainingTime = maxOf(0, LOCK_TIMEOUT_MS - (currentTime - lockTime))
            LockInfo(key, lockTime, remainingTime)
        }.sortedBy { it.remainingTime }
    }
    
    /**
     * 获取锁定统计信息
     * 
     * @return Pair<总锁定数, 即将过期数(剩余时间<3秒)>
     */
    fun getLockStatistics(): Pair<Int, Int> {
        val currentTime = System.currentTimeMillis()
        val total = lockedGameCodes.size
        val soonExpired = lockedGameCodes.count { (_, lockTime) ->
            (currentTime - lockTime) > (LOCK_TIMEOUT_MS - 3000)
        }
        return Pair(total, soonExpired)
    }
    
    /**
     * 检查游戏码是否被锁定
     * 
     * @param prizeId 奖品ID
     * @param gameCode 游戏码
     * @return 是否被锁定
     */
    fun isGameCodeLocked(prizeId: String, gameCode: String): Boolean {
        val key = "$prizeId:$gameCode"
        val lockTime = lockedGameCodes[key] ?: return false
        
        // 检查是否过期
        val currentTime = System.currentTimeMillis()
        if (currentTime - lockTime > LOCK_TIMEOUT_MS) {
            lockedGameCodes.remove(key)
            return false
        }
        
        return true
    }
    
    /**
     * 锁定游戏码
     * 
     * @param prizeId 奖品ID
     * @param gameCode 游戏码
     * @return 是否锁定成功
     */
    fun lockGameCode(prizeId: String, gameCode: String): Boolean {
        val key = "$prizeId:$gameCode"
        val currentTime = System.currentTimeMillis()
        
        // 检查是否已被锁定
        if (isGameCodeLocked(prizeId, gameCode)) {
            return false
        }
        
        // 尝试锁定
        val previousValue = lockedGameCodes.putIfAbsent(key, currentTime)
        val success = previousValue == null
        
        if (success) {
            Log.d(TAG, "Locked game code: $key")
        } else {
            Log.d(TAG, "Failed to lock game code (already locked): $key")
        }
        
        return success
    }
    
    /**
     * 释放游戏码锁定
     * 
     * @param prizeId 奖品ID
     * @param gameCode 游戏码
     * @return 是否释放成功
     */
    fun releaseGameCode(prizeId: String, gameCode: String): Boolean {
        val key = "$prizeId:$gameCode"
        val removed = lockedGameCodes.remove(key) != null
        
        if (removed) {
            Log.d(TAG, "Released game code: $key")
        } else {
            Log.d(TAG, "Game code was not locked: $key")
        }
        
        return removed
    }
    
    /**
     * 批量释放游戏码锁定
     * 
     * @param gameCodeInfoList 游戏码信息列表
     * @return 释放的数量
     */
    fun releaseGameCodes(gameCodeInfoList: List<GameCodeManager.GameCodeInfo>): Int {
        var releasedCount = 0
        gameCodeInfoList.forEach { gameCodeInfo ->
            if (releaseGameCode(gameCodeInfo.prizeId, gameCodeInfo.gameCode)) {
                releasedCount++
            }
        }
        return releasedCount
    }
    
    /**
     * 安全执行助力券操作，自动处理锁定和释放
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @param maxCount 最大获取数量
     * @param timeoutMs 超时时间
     * @param operation 要执行的操作
     * @return 操作结果
     */
    suspend fun <T> safeExecuteWithLock(
        context: Context,
        excludePhoneNumber: String? = null,
        maxCount: Int = 10,
        timeoutMs: Long = 60_000L,
        operation: suspend (List<GameCodeManager.GameCodeInfo>) -> T
    ): T {
        
        Log.d(TAG, "Starting safe operation with lock, excludePhoneNumber: $excludePhoneNumber, maxCount: $maxCount")
        
        // 获取并锁定游戏码
        val gameCodeInfoList = getAndLockGameCodes(context, excludePhoneNumber, maxCount)
        
        return try {
            // 执行操作，带超时控制
            withTimeout(timeoutMs) {
                Log.d(TAG, "Executing operation with ${gameCodeInfoList.size} game codes...")
                val result = operation(gameCodeInfoList)
                Log.d(TAG, "Operation completed successfully")
                result
            }
        } catch (e: TimeoutCancellationException) {
            Log.e(TAG, "Operation timed out after ${timeoutMs}ms", e)
            throw e
        } catch (e: Exception) {
            Log.e(TAG, "Error during operation: ${e.javaClass.simpleName} - ${e.message}", e)
            throw e
        } finally {
            // 确保释放所有锁定的游戏码
            try {
                val releasedCount = releaseGameCodes(gameCodeInfoList)
                Log.d(TAG, "Released $releasedCount out of ${gameCodeInfoList.size} game codes")
            } catch (e: Exception) {
                Log.e(TAG, "Error releasing game codes: ${e.javaClass.simpleName} - ${e.message}", e)
            }
        }
    }
    
    /**
     * 获取并锁定游戏码
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @param maxCount 最大获取数量
     * @return 锁定的游戏码列表
     */
    private fun getAndLockGameCodes(
        context: Context,
        excludePhoneNumber: String? = null,
        maxCount: Int = 10
    ): List<GameCodeManager.GameCodeInfo> {
        
        // 获取所有游戏码
        var allGameCodes = GameCodeManager.getAllGameCodes(context)
        
        // 如果需要排除指定手机号，则进行过滤
        if (excludePhoneNumber != null) {
            allGameCodes = allGameCodes.filter { it.phoneNumber != excludePhoneNumber }
        }
        
        val lockedGameCodes = mutableListOf<GameCodeManager.GameCodeInfo>()
        
        // 尝试锁定游戏码，直到达到最大数量或没有更多可用的游戏码
        for (gameCodeInfo in allGameCodes) {
            if (lockedGameCodes.size >= maxCount) {
                break
            }
            
            // 尝试锁定这个游戏码
            if (lockGameCode(gameCodeInfo.prizeId, gameCodeInfo.gameCode)) {
                lockedGameCodes.add(gameCodeInfo)
            }
        }
        
        Log.d(TAG, "Locked ${lockedGameCodes.size} game codes out of ${allGameCodes.size} available")
        return lockedGameCodes
    }
}
