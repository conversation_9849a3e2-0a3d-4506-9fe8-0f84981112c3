package dev.pigmomo.yhkit2025.utils.boostcoupon

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.utils.ResponseParserUtils
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.utils.FailedTokenIndexRecordUtils
import dev.pigmomo.yhkit2025.utils.GameCodeManager
import dev.pigmomo.yhkit2025.utils.ProcessRecorder
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

/**
 * 助力券发起器
 * 
 * 专门处理助力券的发起逻辑，包括游戏码获取、保存和错误处理
 */
object BoostCouponInitiator {
    private const val TAG = "BoostCouponInitiator"
    
    /**
     * 发起助力券
     * 
     * @param context 上下文
     * @param requestService 请求服务
     * @param progressRecorder 进度记录器
     * @param token 用户令牌
     * @param prizeIds 奖品ID列表
     * @param tokenIndex 令牌索引
     * @param config 配置信息
     * @return 操作结果
     */
    suspend fun initiate(
        context: Context,
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        token: OrderTokenEntity,
        prizeIds: List<String>,
        tokenIndex: Int,
        config: BoostCouponManager.BoostCouponConfig
    ): BoostCouponManager.OperationResult {
        
        var successCount = 0
        var failCount = 0
        val details = mutableListOf<String>()
        
        // 遍历所有助力券ID，获取游戏码并发起助力
        for (prizeId in prizeIds) {
            try {
                progressRecorder.recordProcess("发起助力券: prizeId=$prizeId")
                
                val result = processBoostCouponForPrize(
                    context = context,
                    requestService = requestService,
                    progressRecorder = progressRecorder,
                    token = token,
                    prizeId = prizeId,
                    tokenIndex = tokenIndex,
                    config = config
                )
                
                if (result.success) {
                    successCount++
                    details.add("成功: $prizeId")
                } else {
                    failCount++
                    details.add("失败: $prizeId - ${result.message}")
                }
                
                // 添加间隔，避免请求过快
                delay(config.delayBetweenRequests)
                
            } catch (e: Exception) {
                failCount++
                val errorMsg = "处理助力券 $prizeId 时发生异常: ${e.message}"
                progressRecorder.recordProcess(errorMsg, "ERROR")
                details.add("异常: $prizeId - ${e.message}")
                
                if (tokenIndex > 0) {
                    FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                        FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                        tokenIndex,
                        "助力券"
                    )
                }
                Log.e(TAG, "Exception processing boost coupon: $prizeId", e)
            }
        }
        
        // 记录最终结果
        val finalMessage = "助力券发起完成: 成功${successCount}个, 失败${failCount}个"
        progressRecorder.recordProcess(finalMessage)
        
        return BoostCouponManager.OperationResult.mixed(
            successCount = successCount,
            failCount = failCount,
            message = finalMessage
        ).copy(details = details)
    }
    
    /**
     * 处理单个奖品的助力券发起
     */
    private suspend fun processBoostCouponForPrize(
        context: Context,
        requestService: RequestService,
        progressRecorder: ProcessRecorder,
        token: OrderTokenEntity,
        prizeId: String,
        tokenIndex: Int,
        config: BoostCouponManager.BoostCouponConfig
    ): BoostCouponManager.OperationResult {
        
        // 获取游戏码
        val gameCodeResult = runBlocking {
            requestService.coupon.getGameCode(prizeId)
        }
        
        return when (gameCodeResult) {
            is RequestResult.Success -> {
                handleGameCodeSuccess(
                    context = context,
                    progressRecorder = progressRecorder,
                    token = token,
                    prizeId = prizeId,
                    tokenIndex = tokenIndex,
                    gameCodeResult = gameCodeResult
                )
            }
            
            is RequestResult.Error -> {
                handleGameCodeError(
                    progressRecorder = progressRecorder,
                    prizeId = prizeId,
                    tokenIndex = tokenIndex,
                    error = gameCodeResult.error
                )
            }
        }
    }
    
    /**
     * 处理游戏码获取成功的情况
     */
    private fun handleGameCodeSuccess(
        context: Context,
        progressRecorder: ProcessRecorder,
        token: OrderTokenEntity,
        prizeId: String,
        tokenIndex: Int,
        gameCodeResult: RequestResult.Success<String>
    ): BoostCouponManager.OperationResult {
        
        val response = ResponseParserUtils.parseBoostCouponGameCodeResponse(gameCodeResult.data)
        
        if (response != null && response.code == 0) {
            val gameCode = response.data
            if (gameCode.isNotEmpty()) {
                progressRecorder.recordProcess("助力券参数: prizeId=$prizeId&gameCode=$gameCode")
                
                // 保存游戏码到文件
                val saveResult = saveGameCodeToFile(context, progressRecorder, token, prizeId, gameCode)
                
                return if (saveResult) {
                    BoostCouponManager.OperationResult.success(message = "游戏码获取并保存成功")
                } else {
                    BoostCouponManager.OperationResult.failure(message = "游戏码保存失败")
                }
            } else {
                progressRecorder.recordProcess("助力券 $prizeId 助力券参数为空", "WARNING")
                recordFailedToken(tokenIndex, "助力券参数为空")
                return BoostCouponManager.OperationResult.failure(message = "助力券参数为空")
            }
        } else {
            val errorMsg = "助力券 $prizeId 获取助力券参数错误: ${response?.message ?: "未知错误"}"
            progressRecorder.recordProcess(errorMsg, "ERROR")
            
            // 只有在非上限错误时才记录失败
            if (response?.message?.contains("上限") != true) {
                recordFailedToken(tokenIndex, "获取助力券参数错误")
            }
            
            return BoostCouponManager.OperationResult.failure(message = response?.message ?: "未知错误")
        }
    }
    
    /**
     * 处理游戏码获取失败的情况
     */
    private fun handleGameCodeError(
        progressRecorder: ProcessRecorder,
        prizeId: String,
        tokenIndex: Int,
        error: Throwable
    ): BoostCouponManager.OperationResult {
        
        val errorMsg = "助力券 $prizeId 获取助力券参数请求错误: ${error.message}"
        progressRecorder.recordProcess(errorMsg, "ERROR")
        recordFailedToken(tokenIndex, "请求错误")
        Log.e(TAG, "Error getting game code for prize: $prizeId", error)
        
        return BoostCouponManager.OperationResult.failure(message = error.message ?: "请求错误")
    }
    
    /**
     * 保存游戏码到文件
     */
    private fun saveGameCodeToFile(
        context: Context,
        progressRecorder: ProcessRecorder,
        token: OrderTokenEntity,
        prizeId: String,
        gameCode: String
    ): Boolean {
        
        // 检查游戏码是否已存在，避免重复保存
        if (GameCodeManager.isGameCodeExists(context, prizeId, gameCode)) {
            progressRecorder.recordProcess("助力券参数已存在，跳过保存: prizeId=$prizeId&gameCode=$gameCode")
            return true
        }
        
        val saved = GameCodeManager.saveGameCode(context, prizeId, gameCode, token.phoneNumber)
        
        if (saved) {
            progressRecorder.recordProcess(
                "助力券参数已保存到文件: ${GameCodeManager.getGameCodeFilePath(context)}"
            )
        } else {
            progressRecorder.recordProcess(
                "助力券参数保存到文件错误: prizeId=$prizeId&gameCode=$gameCode",
                "WARNING"
            )
        }
        
        return saved
    }
    
    /**
     * 记录失败的令牌索引
     */
    private fun recordFailedToken(tokenIndex: Int, reason: String) {
        if (tokenIndex > 0) {
            FailedTokenIndexRecordUtils.recordFailedTokenIndex(
                FailedTokenIndexRecordUtils.OperationType.ACTIVITY_OPERATION,
                tokenIndex,
                "助力券"
            )
        }
    }
}
