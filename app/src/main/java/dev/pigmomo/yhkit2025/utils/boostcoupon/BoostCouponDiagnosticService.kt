package dev.pigmomo.yhkit2025.utils.boostcoupon

import android.content.Context
import android.util.Log
import dev.pigmomo.yhkit2025.utils.FileUtils
import dev.pigmomo.yhkit2025.utils.GameCodeManager

/**
 * 助力券诊断服务
 * 
 * 提供助力券状态的诊断和监控功能，帮助快速定位问题
 */
object BoostCouponDiagnosticService {
    private const val TAG = "BoostCouponDiagnostic"
    
    /**
     * 诊断结果数据类
     */
    data class DiagnosticResult(
        val fileStatus: FileStatus,
        val gameCodeStatus: GameCodeStatus,
        val lockStatus: LockStatus,
        val summary: String,
        val recommendations: List<String>
    )
    
    /**
     * 文件状态
     */
    data class FileStatus(
        val exists: Boolean,
        val size: Long,
        val lineCount: Int,
        val emptyLines: Int,
        val validLines: Int,
        val invalidLines: Int
    )
    
    /**
     * 游戏码状态
     */
    data class GameCodeStatus(
        val totalCodes: Int,
        val availableCodes: Int,
        val lockedCodes: Int,
        val codesByPrizeId: Map<String, Int>,
        val codesByPhoneNumber: Map<String, Int>
    )
    
    /**
     * 锁定状态
     */
    data class LockStatus(
        val totalLocks: Int,
        val soonExpiredLocks: Int,
        val lockDetails: List<BoostCouponLockManager.LockInfo>
    )
    
    /**
     * 执行完整的助力券状态诊断
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @return 诊断结果
     */
    fun performFullDiagnosis(
        context: Context,
        excludePhoneNumber: String? = null
    ): DiagnosticResult {
        Log.d(TAG, "Starting full boost coupon diagnosis...")
        
        val fileStatus = diagnoseFileStatus(context)
        val gameCodeStatus = diagnoseGameCodeStatus(context, excludePhoneNumber)
        val lockStatus = diagnoseLockStatus()
        
        val summary = generateSummary(fileStatus, gameCodeStatus, lockStatus)
        val recommendations = generateRecommendations(fileStatus, gameCodeStatus, lockStatus)
        
        val result = DiagnosticResult(
            fileStatus = fileStatus,
            gameCodeStatus = gameCodeStatus,
            lockStatus = lockStatus,
            summary = summary,
            recommendations = recommendations
        )
        
        Log.d(TAG, "Diagnosis completed: $summary")
        return result
    }
    
    /**
     * 快速诊断 - 仅检查关键状态
     * 
     * @param context 上下文
     * @param excludePhoneNumber 要排除的手机号码
     * @return 诊断结果字符串
     */
    fun quickDiagnosis(context: Context, excludePhoneNumber: String? = null): String {
        val fileExists = FileUtils.fileExists(context, "gameCodeList.txt")
        val fileSize = FileUtils.getFileSize(context, "gameCodeList.txt")
        val allCodes = GameCodeManager.getAllGameCodes(context)
        val filteredCodes = if (excludePhoneNumber != null) {
            allCodes.filter { it.phoneNumber != excludePhoneNumber }
        } else {
            allCodes
        }
        
        val availableCodes = filteredCodes.count { 
            !BoostCouponLockManager.isGameCodeLocked(it.prizeId, it.gameCode) 
        }
        
        return "文件: ${if (fileExists) "存在" else "不存在"} (${fileSize}B), " +
                "游戏码: ${filteredCodes.size}个, " +
                "可用: ${availableCodes}个"
    }
    
    /**
     * 诊断文件状态
     */
    private fun diagnoseFileStatus(context: Context): FileStatus {
        val fileName = "gameCodeList.txt"
        val exists = FileUtils.fileExists(context, fileName)
        val size = FileUtils.getFileSize(context, fileName)
        
        var lineCount = 0
        var emptyLines = 0
        var validLines = 0
        var invalidLines = 0
        
        if (exists) {
            try {
                val content = FileUtils.readFile(context, fileName)
                val lines = content.split("\n")
                lineCount = lines.size
                
                lines.forEach { line ->
                    when {
                        line.trim().isEmpty() -> emptyLines++
                        isValidGameCodeLine(line) -> validLines++
                        else -> invalidLines++
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error reading file for diagnosis", e)
            }
        }
        
        return FileStatus(
            exists = exists,
            size = size,
            lineCount = lineCount,
            emptyLines = emptyLines,
            validLines = validLines,
            invalidLines = invalidLines
        )
    }
    
    /**
     * 诊断游戏码状态
     */
    private fun diagnoseGameCodeStatus(context: Context, excludePhoneNumber: String?): GameCodeStatus {
        val allCodes = GameCodeManager.getAllGameCodes(context)
        val filteredCodes = if (excludePhoneNumber != null) {
            allCodes.filter { it.phoneNumber != excludePhoneNumber }
        } else {
            allCodes
        }
        
        val availableCodes = filteredCodes.count { 
            !BoostCouponLockManager.isGameCodeLocked(it.prizeId, it.gameCode) 
        }
        val lockedCodes = filteredCodes.size - availableCodes
        
        val codesByPrizeId = filteredCodes.groupBy { it.prizeId }.mapValues { it.value.size }
        val codesByPhoneNumber = filteredCodes.groupBy { it.phoneNumber }.mapValues { it.value.size }
        
        return GameCodeStatus(
            totalCodes = filteredCodes.size,
            availableCodes = availableCodes,
            lockedCodes = lockedCodes,
            codesByPrizeId = codesByPrizeId,
            codesByPhoneNumber = codesByPhoneNumber
        )
    }
    
    /**
     * 诊断锁定状态
     */
    private fun diagnoseLockStatus(): LockStatus {
        val (totalLocks, soonExpiredLocks) = BoostCouponLockManager.getLockStatistics()
        val lockDetails = BoostCouponLockManager.getLockStatusInfo()
        
        return LockStatus(
            totalLocks = totalLocks,
            soonExpiredLocks = soonExpiredLocks,
            lockDetails = lockDetails
        )
    }
    
    /**
     * 生成诊断摘要
     */
    private fun generateSummary(
        fileStatus: FileStatus,
        gameCodeStatus: GameCodeStatus,
        lockStatus: LockStatus
    ): String {
        val fileStatusText = if (fileStatus.exists) {
            "文件存在(${fileStatus.size}B, ${fileStatus.validLines}有效行)"
        } else {
            "文件不存在"
        }
        
        val gameCodeStatusText = "游戏码${gameCodeStatus.totalCodes}个(可用${gameCodeStatus.availableCodes}个)"
        val lockStatusText = if (lockStatus.totalLocks > 0) {
            "锁定${lockStatus.totalLocks}个"
        } else {
            "无锁定"
        }
        
        return "$fileStatusText, $gameCodeStatusText, $lockStatusText"
    }
    
    /**
     * 生成建议
     */
    private fun generateRecommendations(
        fileStatus: FileStatus,
        gameCodeStatus: GameCodeStatus,
        lockStatus: LockStatus
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        // 文件相关建议
        if (!fileStatus.exists) {
            recommendations.add("游戏码文件不存在，请先发起助力券获取游戏码")
        } else if (fileStatus.validLines == 0) {
            recommendations.add("游戏码文件为空或无有效数据，请检查文件内容")
        } else if (fileStatus.invalidLines > 0) {
            recommendations.add("发现${fileStatus.invalidLines}行无效数据，建议清理文件")
        }
        
        // 游戏码相关建议
        if (gameCodeStatus.totalCodes == 0) {
            recommendations.add("没有可用的游戏码，请先发起助力券")
        } else if (gameCodeStatus.availableCodes == 0) {
            recommendations.add("所有游戏码都被锁定，请等待锁定释放或强制清理锁定")
        } else if (gameCodeStatus.availableCodes < 5) {
            recommendations.add("可用游戏码较少(${gameCodeStatus.availableCodes}个)，建议发起更多助力券")
        }
        
        // 锁定相关建议
        if (lockStatus.totalLocks > 50) {
            recommendations.add("锁定数量过多(${lockStatus.totalLocks}个)，建议调用强制清理")
        } else if (lockStatus.soonExpiredLocks > 0) {
            recommendations.add("有${lockStatus.soonExpiredLocks}个锁定即将过期，将自动释放")
        }
        
        // 分布相关建议
        if (gameCodeStatus.codesByPrizeId.size == 1) {
            recommendations.add("游戏码只来自一个奖品，建议发起多个不同奖品的助力券")
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("状态正常，可以正常进行助力券操作")
        }
        
        return recommendations
    }
    
    /**
     * 检查是否为有效的游戏码行
     */
    private fun isValidGameCodeLine(line: String): Boolean {
        return try {
            val parts = line.split(",")
            parts.size >= 4 && parts[2].isNotEmpty() && parts[3].isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取锁定监控信息
     */
    fun getLockMonitorInfo(): String {
        val lockInfo = BoostCouponLockManager.getLockStatusInfo()
        if (lockInfo.isEmpty()) {
            return "当前无锁定状态"
        }
        
        val sb = StringBuilder()
        sb.append("当前锁定状态 (${lockInfo.size}个):\n")
        lockInfo.take(10).forEach { lock ->
            sb.append("- ${lock.key}: 剩余${lock.remainingTime}ms\n")
        }
        
        if (lockInfo.size > 10) {
            sb.append("... 还有${lockInfo.size - 10}个锁定")
        }
        
        return sb.toString()
    }
}
